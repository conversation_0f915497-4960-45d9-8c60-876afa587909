"""
Enhanced Data Loader for ERP system

Loads parsed XML data into the database using modular processors, comprehensive
validation, and transformation capabilities. This implementation provides a
complete data loading pipeline with proper error handling and recovery.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime

from .parser import XMLDataParser, ParseMode
from .processors.manager import ProcessorManager, ProcessingResult
from .validation import DataValidator, ValidationResult
from .validation.transformers import DataTransformer
from .exceptions import DataLoadingError, ModelNotFoundError, RecordCreationError
from .xmlid_manager import XMLIDManager
from .sql_helpers import SQLHelpers, ModelSQLHelpers
from ..logging import get_logger
from ..database.connection.manager import DatabaseManager


class DataLoader:
    """
    Enhanced Data Loader for ERP system

    Provides comprehensive data loading capabilities with modular processors,
    validation, transformation, and error handling. This implementation uses
    a pipeline approach for maximum flexibility and maintainability.
    """

    def __init__(self, db_manager: DatabaseManager, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Enhanced DataLoader

        Args:
            db_manager: Database manager instance
            config: Configuration options for the loader
        """
        self.db_manager = db_manager
        self.logger = get_logger(__name__)
        self.config = config or {}

        # Initialize core components
        self.parser = XMLDataParser(
            mode=ParseMode(self.config.get('parse_mode', 'strict')),
            context=self.config.get('parser_context', {})
        )
        self.processor_manager = ProcessorManager(db_manager)
        self.validator = DataValidator()
        self.transformer = DataTransformer()

        # Legacy SQL helpers for backward compatibility
        self.sql = SQLHelpers(db_manager)
        self.model_sql = ModelSQLHelpers(self.sql)
        self.xmlid_manager = XMLIDManager(db_manager)

        # Configuration
        self.enable_validation = self.config.get('enable_validation', True)
        self.enable_transformation = self.config.get('enable_transformation', True)
        self.continue_on_error = self.config.get('continue_on_error', True)
        self.parallel_processing = self.config.get('parallel_processing', False)

        # Configure processor manager
        self.processor_manager.set_parallel_processing(
            self.parallel_processing,
            self.config.get('max_concurrent_processors', 3)
        )
        self.processor_manager.set_error_handling(self.continue_on_error)

        # Statistics
        self.stats = {
            'files_processed': 0,
            'records_processed': 0,
            'records_successful': 0,
            'records_failed': 0,
            'validation_errors': 0,
            'transformation_errors': 0,
            'processing_errors': 0
        }
        
    async def load_data_file(self, file_path: str, addon_name: str = None, **kwargs) -> Dict[str, Any]:
        """
        Load data from an XML file using the enhanced pipeline

        Args:
            file_path: Path to the XML data file
            addon_name: Name of the addon (for XML ID namespacing)
            **kwargs: Additional options for loading

        Returns:
            Comprehensive loading results with statistics and errors
        """
        start_time = datetime.utcnow()

        try:
            self.logger.info(f"Loading data file: {file_path}")
            self.stats['files_processed'] += 1

            # Parse the XML file
            parse_result = self.parser.parse_file(file_path, addon_name)

            if not parse_result['records']:
                self.logger.warning(f"No records found in file: {file_path}")
                return self._create_empty_result(file_path, parse_result)

            # Process through the enhanced pipeline
            processing_result = await self._process_data_pipeline(
                parse_result['records'],
                addon_name=addon_name,
                file_path=file_path,
                **kwargs
            )

            # Create comprehensive result
            result = self._create_comprehensive_result(
                file_path, parse_result, processing_result, start_time
            )

            self.logger.info(
                f"Completed loading {file_path}: "
                f"{processing_result.successful_items}/{processing_result.total_items} successful"
            )

            return result

        except Exception as e:
            error_msg = f"Failed to load data file {file_path}: {e}"
            self.logger.error(error_msg)
            return self._create_error_result(file_path, error_msg, start_time)

    async def load_data_content(self, xml_content: str, addon_name: str = None, **kwargs) -> Dict[str, Any]:
        """
        Load data from XML content string using the enhanced pipeline

        Args:
            xml_content: XML content as string
            addon_name: Name of the addon (for XML ID namespacing)
            **kwargs: Additional options for loading

        Returns:
            Comprehensive loading results with statistics and errors
        """
        start_time = datetime.utcnow()

        try:
            self.logger.info("Loading XML content")

            # Parse the XML content
            parse_result = self.parser.parse_content(xml_content, {'addon_name': addon_name})

            if not parse_result['records']:
                self.logger.warning("No records found in XML content")
                return self._create_empty_result('content', parse_result)

            # Process through the enhanced pipeline
            processing_result = await self._process_data_pipeline(
                parse_result['records'],
                addon_name=addon_name,
                source='content',
                **kwargs
            )

            # Create comprehensive result
            result = self._create_comprehensive_result(
                'content', parse_result, processing_result, start_time
            )

            self.logger.info(
                f"Completed loading XML content: "
                f"{processing_result.successful_items}/{processing_result.total_items} successful"
            )

            return result

        except Exception as e:
            error_msg = f"Failed to load XML content: {e}"
            self.logger.error(error_msg)
            return self._create_error_result('content', error_msg, start_time)
    
    async def _process_data_pipeline(self, records: List[Dict[str, Any]], **context) -> ProcessingResult:
        """Process data through the enhanced pipeline"""
        self.logger.info(f"Processing {len(records)} records through enhanced pipeline")

        # Step 1: Validation
        if self.enable_validation:
            validation_result = await self._validate_data(records, context)
            if not validation_result.valid and not self.continue_on_error:
                raise DataLoadingError(f"Validation failed with {validation_result.error_count} errors")

        # Step 2: Transformation
        if self.enable_transformation:
            records = await self._transform_data(records, context)

        # Step 3: Processing
        processing_result = await self.processor_manager.process_data(records, context)

        # Update statistics
        self.stats['records_processed'] += processing_result.total_items
        self.stats['records_successful'] += processing_result.successful_items
        self.stats['records_failed'] += processing_result.failed_items

        return processing_result

    async def _validate_data(self, records: List[Dict[str, Any]], context: Dict[str, Any]) -> ValidationResult:
        """Validate data records"""
        self.logger.debug(f"Validating {len(records)} records")

        try:
            validation_result = self.validator.validate_data(records, context)
            self.stats['validation_errors'] += validation_result.error_count

            if validation_result.error_count > 0:
                self.logger.warning(f"Validation found {validation_result.error_count} errors")
                for error in validation_result.errors:
                    self.logger.warning(f"Validation error: {error.message}")

            return validation_result

        except Exception as e:
            self.logger.error(f"Validation process failed: {e}")
            self.stats['validation_errors'] += 1
            raise

    async def _transform_data(self, records: List[Dict[str, Any]], context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Transform data records"""
        self.logger.debug(f"Transforming {len(records)} records")

        try:
            transformation_results = self.transformer.transform_data(records, context)
            transformed_records = []

            for result in transformation_results:
                if result.success:
                    transformed_records.append(result.transformed_value)
                else:
                    # Keep original record if transformation failed
                    transformed_records.append(result.original_value)
                    self.stats['transformation_errors'] += len(result.errors)
                    for error in result.errors:
                        self.logger.warning(f"Transformation error: {error}")

            return transformed_records

        except Exception as e:
            self.logger.error(f"Transformation process failed: {e}")
            self.stats['transformation_errors'] += 1
            return records  # Return original records on failure
    
    def _create_comprehensive_result(self, source: str, parse_result: Dict[str, Any],
                                   processing_result: ProcessingResult, start_time: datetime) -> Dict[str, Any]:
        """Create comprehensive loading result"""
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()

        return {
            'source': source,
            'success': processing_result.successful_items > 0,
            'duration': duration,
            'statistics': {
                'total_records': processing_result.total_items,
                'successful_records': processing_result.successful_items,
                'failed_records': processing_result.failed_items,
                'skipped_records': processing_result.skipped_items,
                'success_rate': processing_result.success_rate,
                'validation_errors': self.stats['validation_errors'],
                'transformation_errors': self.stats['transformation_errors']
            },
            'parsing': {
                'records_found': len(parse_result['records']),
                'parse_errors': len(parse_result.get('errors', [])),
                'parse_warnings': len(parse_result.get('warnings', [])),
                'parse_stats': parse_result.get('stats', {})
            },
            'processing': {
                'processor_results': {
                    name: {
                        'processed': result.processed_count,
                        'successful': result.success_count,
                        'errors': result.error_count,
                        'warnings': result.warning_count,
                        'success_rate': result.success_rate
                    }
                    for name, result in processing_result.processor_results.items()
                }
            },
            'errors': processing_result.errors,
            'warnings': processing_result.warnings,
            'metadata': {
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'loader_config': self.config,
                'context': parse_result.get('context', {})
            }
        }

    def _create_empty_result(self, source: str, parse_result: Dict[str, Any]) -> Dict[str, Any]:
        """Create result for empty data"""
        return {
            'source': source,
            'success': True,
            'duration': 0,
            'statistics': {
                'total_records': 0,
                'successful_records': 0,
                'failed_records': 0,
                'skipped_records': 0,
                'success_rate': 100.0
            },
            'parsing': {
                'records_found': 0,
                'parse_errors': len(parse_result.get('errors', [])),
                'parse_warnings': len(parse_result.get('warnings', [])),
                'parse_stats': parse_result.get('stats', {})
            },
            'errors': [],
            'warnings': ['No records found to process'],
            'metadata': parse_result.get('context', {})
        }

    def _create_error_result(self, source: str, error_msg: str, start_time: datetime) -> Dict[str, Any]:
        """Create result for error cases"""
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()

        return {
            'source': source,
            'success': False,
            'duration': duration,
            'statistics': {
                'total_records': 0,
                'successful_records': 0,
                'failed_records': 0,
                'skipped_records': 0,
                'success_rate': 0.0
            },
            'errors': [error_msg],
            'warnings': [],
            'metadata': {
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'error': True
            }
        }

    # Legacy methods for backward compatibility
    async def _load_records(self, records: List[Dict[str, Any]], addon_name: str = None) -> Dict[str, Any]:
        """Legacy method for backward compatibility"""
        self.logger.warning("Using legacy _load_records method. Consider using the enhanced pipeline.")

        result = {
            'loaded': 0,
            'updated': 0,
            'skipped': 0,
            'errors': []
        }

        for record_def in records:
            try:
                success = await self._load_single_record(record_def, addon_name)
                if success:
                    result['loaded'] += 1
                else:
                    result['skipped'] += 1

            except Exception as e:
                error_msg = f"Failed to load record {record_def.get('xml_id', 'unknown')}: {e}"
                result['errors'].append(error_msg)
                self.logger.error(error_msg)

        return result

    async def _load_single_record(self, record_def: Dict[str, Any], addon_name: str = None) -> bool:
        """Load a single record into the database using raw SQL"""
        model_name = record_def['model']
        xml_id = record_def.get('xml_id')
        values = record_def['values']
        noupdate = record_def.get('noupdate', False)

        # Check if model table exists
        table_name = model_name.replace('.', '_')
        if not await self.sql.table_exists(table_name):
            raise ModelNotFoundError(f"Table {table_name} for model {model_name} not found")

        # Process field values
        processed_values = await self._process_field_values(values, model_name)

        # Check if record already exists (by XML ID)
        existing_record_id = None
        if xml_id:
            existing_record_id = await self._find_record_id_by_xml_id(xml_id, addon_name)

        if existing_record_id:
            if not noupdate:
                # Update existing record
                success = await self.model_sql.update_record(model_name, existing_record_id, processed_values)
                if success:
                    self.logger.debug(f"Updated record {xml_id} in {model_name}")
                return success
            else:
                self.logger.debug(f"Skipped updating record {xml_id} (noupdate=True)")
                return True
        else:
            # Create new record
            new_record_id = await self.model_sql.create_record(model_name, processed_values)
            if not new_record_id:
                raise RecordCreationError(f"Failed to create record in {model_name}")

            # Store XML ID mapping if provided
            if xml_id:
                await self._store_xml_id_mapping(xml_id, addon_name, model_name, new_record_id)

            self.logger.debug(f"Created record {xml_id or 'no-id'} in {model_name} with ID {new_record_id}")
            return True
    
    async def _process_field_values(self, values: Dict[str, Any], model_name: str) -> Dict[str, Any]:
        """Process field values, handling references and evaluations using raw SQL"""
        processed = {}

        for field_name, field_def in values.items():
            if isinstance(field_def, dict):
                field_type = field_def.get('type')
                field_value = field_def.get('value')

                if field_type == 'ref':
                    # Reference to another record
                    resolved_id = await self._resolve_reference(field_value)
                    processed[field_name] = resolved_id
                elif field_type == 'eval':
                    # Python expression to evaluate
                    processed[field_name] = self._evaluate_expression(field_value)
                else:
                    # Text value
                    processed[field_name] = field_value
            else:
                # Direct value
                processed[field_name] = field_def

        return processed
    
    async def _resolve_reference(self, ref_value: str) -> Optional[str]:
        """Resolve a reference to another record using raw SQL"""
        try:
            # Use XML ID manager to resolve the reference
            record_id = await self.xmlid_manager.resolve_xmlid_to_record_id(ref_value)
            if record_id:
                return record_id

            self.logger.debug(f"Reference not found: {ref_value}")
            return None

        except Exception as e:
            self.logger.debug(f"Failed to resolve reference {ref_value}: {e}")
            return None
    
    def _evaluate_expression(self, expression: str) -> Any:
        """Safely evaluate a Python expression"""
        # For now, handle simple cases
        # In production, this should use a safe evaluation context
        try:
            # Handle common cases
            if expression == 'True':
                return True
            elif expression == 'False':
                return False
            elif expression == 'None':
                return None
            elif expression.startswith("'") and expression.endswith("'"):
                return expression[1:-1]  # String literal
            elif expression.startswith('"') and expression.endswith('"'):
                return expression[1:-1]  # String literal
            else:
                # Try to evaluate as number
                try:
                    return int(expression)
                except ValueError:
                    try:
                        return float(expression)
                    except ValueError:
                        return expression  # Return as string
        except Exception:
            return expression
    
    async def _model_exists(self, model_name: str) -> bool:
        """Check if a model table exists using raw SQL"""
        table_name = model_name.replace('.', '_')
        return await self.sql.table_exists(table_name)
    
    async def _find_record_id_by_xml_id(self, xml_id: str, addon_name: str = None) -> Optional[str]:
        """Find a record ID by its XML ID using raw SQL"""
        try:
            # Build full XML ID
            if addon_name and '.' not in xml_id:
                full_xml_id = f"{addon_name}.{xml_id}"
            else:
                full_xml_id = xml_id

            # Use XML ID manager to resolve
            record_id = await self.xmlid_manager.resolve_xmlid_to_record_id(full_xml_id)
            return record_id

        except Exception as e:
            self.logger.debug(f"Failed to find record by XML ID {xml_id}: {e}")
            return None
    
    async def _store_xml_id_mapping(self, xml_id: str, addon_name: str, model_name: str, record_id: str):
        """Store XML ID to record ID mapping using raw SQL"""
        try:
            # Use addon_name or default to 'base' if not provided
            module = addon_name or 'base'

            # Create or update the XML ID mapping using XML ID manager
            success = await self.xmlid_manager.create_xmlid_mapping(
                module=module,
                name=xml_id,
                model=model_name,
                res_id=str(record_id),
                noupdate=False
            )

            if success:
                self.logger.debug(f"Stored XML ID mapping: {module}.{xml_id} -> {model_name}({record_id})")
            else:
                self.logger.error(f"Failed to store XML ID mapping for {xml_id}")

        except Exception as e:
            self.logger.error(f"Failed to store XML ID mapping for {xml_id}: {e}")
